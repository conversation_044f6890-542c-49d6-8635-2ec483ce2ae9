import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../constants/emotion_status.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 情绪状态统计界面
class EmotionStatusStatisticsScreen extends StatefulWidget {
  @override
  _EmotionStatusStatisticsScreenState createState() => _EmotionStatusStatisticsScreenState();
}

class _EmotionStatusStatisticsScreenState extends State<EmotionStatusStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.line;
  DateTime _currentDate = DateTime.now();
  Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  /// 多曲线图中各情绪状态的可见性控制
  Map<EmotionStatus, bool> _visibleStatuses = {
    for (EmotionStatus status in EmotionStatus.values) 
    status: EmotionStatus.values.indexOf(status) == 0 ? true : false,
  };

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false).device?.deviceName ?? '';
    ChartService chartService = Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchEmotionStatusChartData(device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchEmotionStatusChartData(device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchEmotionStatusChartData(device_name, 'month', _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  /// 聚合情绪数据按日期分组
  Map<EmotionStatus, List<FlSpot>> _aggregateEmotionDataByDate() {
    final Map<EmotionStatus, List<FlSpot>> result = {};
    // 初始化所有状态的数据列表
    for (final status in EmotionStatus.values) {
      double x = 0;
      for (final data in _healthData[status.name]) {
        result[status] ??= [];
          result[status]!.add(FlSpot(x, data.toDouble()));
          x++;
        }
    } 
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('情绪状态统计'),
        backgroundColor: Colors.purple.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动选择对应的图表类型
                switch (period) {
                  case TimePeriod.day:
                    _selectedChartType = ChartViewType.line;
                    break;
                  case TimePeriod.week:
                    _selectedChartType = ChartViewType.groupedBar;
                    break;
                  case TimePeriod.month:
                    _selectedChartType = ChartViewType.multiLine;
                    break;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型选择器
          ChartViewSelector(
            availableTypes: ChartTypePresets.getEmotionStatusStatistics(_selectedPeriod),
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getChartTitle(),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),

          // 多曲线图图例
          if (!_isLoading && _selectedChartType == ChartViewType.multiLine)
            _buildEmotionStatusLegend(),

          // 统计信息
          // if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 获取图表标题
  String _getChartTitle() {
    switch (_selectedChartType) {
      case ChartViewType.line:
        return '情绪水平变化';
      case ChartViewType.groupedBar:
        return '情绪状态分布';
      case ChartViewType.multiLine:
        return '各情绪状态趋势';
      default:
        return '情绪状态统计';
    }
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData!.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.line:
        return _buildLineChart();
      case ChartViewType.groupedBar:
        return _buildGroupedBarChart();
      case ChartViewType.multiLine:
        return _buildMultiLineChart();
      default:
        return _buildLineChart();
    }
  }

  /// 构建日模式曲线图（情绪水平变化）
  Widget _buildLineChart() {
    
    // 构建FlSpot列表
    List<FlSpot> spots = [];
    double k = 0;
    for( final data in _healthData['level']!){
      spots.add(FlSpot(k,data.toDouble()));
      k++;
    }

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                    child: Text(
                      _getBottomTitle(value.toInt()),
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 25, // 进一步减少右侧预留空间，让绘图区域更靠近右边缘
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 4.0), // 减少左侧padding
                    child: Text(
                      '${value.toInt()}',
                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                      textAlign: TextAlign.right,
                    ),
                  );
                },
              ),
            ), // 显示右侧情绪等级标题
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 去掉坐标轴外框
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final index = barSpot.x.toInt();
                  String timeRange = '';

                  if (_selectedPeriod == TimePeriod.day) {
                    // 日模式显示时间段（5分钟间隔）
                    final hour = index ~/ 12;
                    final minute = (index % 12) * 5;
                    final nextMinute = minute + 5;
                    if (nextMinute == 60) {
                      timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                    } else {
                      timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                    }
                  } else if (_selectedPeriod == TimePeriod.month) {
                    // 月模式显示具体日期
                    final day = index + 1;
                    final month = _currentDate.month; // 使用当前月份
                    timeRange = '${month}月${day}日';
                  } else {
                    // 周模式显示具体日期
                    DateTime monday = _currentDate.subtract(Duration(days: _currentDate.weekday - 1));
                    final weekdays =List.generate(7, (index) => DateTime(
                      monday.year,
                      monday.month,
                      monday.day + index, // Dart 会自动处理日期溢出
                    ));
                    timeRange = '${weekdays[index].month}月${weekdays[index].day}日';
                  }

                  return LineTooltipItem(
                    '$timeRange\n情绪等级${barSpot.y.toInt()}',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
            ),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true, // 使用光滑曲线
              curveSmoothness: 0.35, // 设置曲线平滑度，值越大越平滑
              color: Colors.purple,
              barWidth: 3,
              dotData: FlDotData(show: false), // 不显示曲线上的小圆点
              belowBarData: BarAreaData(
                show: true,
                color: Colors.purple.withOpacity(0.1),
              ),
            ),
          ],
        // 添加情绪水平参考线
        extraLinesData: ExtraLinesData(
          horizontalLines: [
            HorizontalLine(
              y: 4.0, // 中性情绪线
              color: Colors.grey.withOpacity(0.5),
              strokeWidth: 2,
              dashArray: [5, 5],
            ),
          ],
        ),
      ),
    ),
    );
  }
  
  /// 日模式获取底部标题间隔
  double _getBottomTitleInterval() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return 36.0; // 日模式：3小时间隔(36个5分钟)
      case TimePeriod.week:
        return 1.0; // 周模式：每天显示
      case TimePeriod.month:
        return 5.0; // 月模式：每5天显示一个标签
    }
  }

  /// 日模式获取底部标题
  String _getBottomTitle(int index) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${(index ~/ 12)}:${((index % 12) * 5).toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${(index + 1)}';
      case TimePeriod.month:
        return '${(index + 1)}日';
    }
  }

  /// 构建周模式簇状柱形图（7种情绪状态分布）
  Widget _buildGroupedBarChart() {
    
    // 构建BarChartGroupData列表
    final barGroups = <BarChartGroupData>[];
    for (int i = 0; i < 7; i++) {
      final barRodData = <BarChartRodData>[];
      for(final status in EmotionStatus.values){
        final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));
        barRodData.add(
          BarChartRodData(
            toY: _healthData[status.name]![i].toDouble(),
            color: color,
            width: 3,
            borderRadius: BorderRadius.circular(1),
          ),
        );
      }
      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: barRodData,
        ),
      );
    } 

    // 计算Y轴的最大值和间隔
    // final maxValue = groupedData.values
    //     .expand((map) => map.values)
    //     .reduce((a, b) => a > b ? a : b)
    //     .toDouble();
    // final yAxisInterval = _getBarChartYAxisInterval(maxValue);

    return Column(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: 2.0, // 设置宽高比为2:1
            child: BarChart(
              BarChartData(
                gridData: FlGridData(
                  show: true,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.shade300,
                      strokeWidth: 1,
                      dashArray: [5, 5], // 设置虚线样式
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 25, // 进一步减少右侧预留空间，让绘图区域更靠近右边缘
                      // interval: yAxisInterval, // 设置Y轴间隔，避免重复标签
                      getTitlesWidget: (value, meta) {
                        // 只显示整数值，避免重复的0
                        // if (value % yAxisInterval != 0) return Container();
                        return Padding(
                          padding: const EdgeInsets.only(left: 4.0), // 减少左侧padding
                          child: Text(
                            value.toInt().toString(),
                            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                            textAlign: TextAlign.right,
                          ),
                        );
                      },
                    ),
                  ), // 显示右侧数值标题
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                    getTitlesWidget: (value, meta) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                        child: Text(
                          _getGroupTitle(value.toInt()),
                          style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                        ),
                      );
                    },
                  ),
                ),
                topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              // maxY: (maxValue * 1.1).ceilToDouble(), // 设置Y轴最大值，留出10%的空间
              borderData: FlBorderData(show: false), // 去掉坐标轴外框
              barTouchData: BarTouchData(
                enabled: true,
                touchTooltipData: BarTouchTooltipData(
                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                    final groupTitle = _getGroupTitle(groupIndex);
                    final emotionStatus = EmotionStatus.values[rodIndex];
                    return BarTooltipItem(
                      '$groupTitle\n${emotionStatus.displayName}: ${rod.toY.toInt()}次',
                      const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
              barGroups: barGroups,
            ),
          ),
          ),
        ),
        const SizedBox(height: 16),
        // 图例 - 移动到图表下方，显示7种情绪状态
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 8,
          runSpacing: 4,
          children: EmotionStatus.values.map((status) {
            final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));
            return _buildLegendItem(status.displayName, color);
          }).toList(),
        ),
      ],
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 获取分组索引
  int _getGroupIndex(int dataIndex) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return dataIndex ~/ 12; // 每12个数据点为一组（1小时，5分钟间隔）
      case TimePeriod.week:
        return dataIndex ~/ 15; // 每15个数据点为一组（每天一组，因为每天有15个数据点）
      case TimePeriod.month:
        return dataIndex ~/ 7; // 每周一组
    }
  }

  /// 获取分组标题
  String _getGroupTitle(int groupIndex) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = groupIndex * 1; // 每小时
        return '${hour}时';
      case TimePeriod.week:
        final date = _currentDate.subtract(Duration(days: 6 - groupIndex));
        return '${date.month}/${date.day}';
      case TimePeriod.month:
        final weekNumber = groupIndex + 1;
        return '第${weekNumber}周';
    }
  }

  /// 获取柱状图Y轴间隔
  double _getBarChartYAxisInterval(double maxValue) {
    if (maxValue <= 0) return 1.0;

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建多曲线图（月模式：7条情绪状态曲线）
  Widget _buildMultiLineChart() {

    final aggregatedData = _aggregateEmotionDataByDate();
    
    // 过滤出可见的状态数据
    final visibleData = <EmotionStatus, List<FlSpot>>{};
    for (final entry in aggregatedData.entries) {
      if (_visibleStatuses[entry.key] == true && entry.value.isNotEmpty) {
        visibleData[entry.key] = entry.value;
      }
    }
    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                interval: 6.0, // 每6天显示一个标签
                getTitlesWidget: (value, meta) {
                  final day = value.toInt() + 1;
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                    child: Text(
                      '${day}日',
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 25, // 进一步减少右侧预留空间，让绘图区域更靠近右边缘
                interval: _getYAxisInterval(visibleData), // 设置Y轴间隔
                getTitlesWidget: (value, meta) {
                  final interval = _getYAxisInterval(visibleData).toInt();
                  final intValue = value.toInt();

                  // 只显示符合间隔的非负整数值
                  if (intValue >= 0 && interval > 0 && intValue % interval == 0) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 4.0), // 减少左侧padding
                      child: Text(
                        intValue.toString(),
                        style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                        textAlign: TextAlign.right,
                      ),
                    );
                  }
                  return SizedBox.shrink(); // 不显示非间隔值
                },
              ),
            ), // 显示右侧数值标题
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 去掉坐标轴外框
          lineTouchData: LineTouchData(
            enabled: true,
            handleBuiltInTouches: true, // 确保内置触摸处理正常
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return _buildMultiLineTooltipItems(touchedBarSpots);
              },
            ),
            touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
              // 不执行任何状态更新，只处理tooltip显示
              // 这样可以避免触摸时意外的状态改变
            },
          ),
          lineBarsData: _buildMultiLineChartBars(visibleData),
        ),
      ),
    );
  }

  /// 获取Y轴最大值（用于多曲线图）
  double _getMaxYValue(Map<EmotionStatus, List<FlSpot>> visibleData) {
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 获取Y轴间隔（用于多曲线图）
  double _getYAxisInterval(Map<EmotionStatus, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 1.0;

    final maxValue = _getMaxYValue(visibleData);

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建多曲线图的曲线数据
  List<LineChartBarData> _buildMultiLineChartBars(Map<EmotionStatus, List<FlSpot>> visibleData) {
    final List<LineChartBarData> lineBars = [];

    for (final entry in visibleData.entries) {
      final status = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

      lineBars.add(
        LineChartBarData(
          spots: spots,
          isCurved: true, // 使用光滑曲线
          curveSmoothness: 0.35, // 设置曲线平滑度，值越大越平滑
          color: color,
          barWidth: 1.5, // 线条宽度
          dotData: FlDotData(show: false), // 不显示曲线上的小圆点
          belowBarData: BarAreaData(show: false), // 多曲线图不显示填充区域
        ),
      );
    }

    return lineBars;
  }

  /// 构建多曲线图的Tooltip项
  List<LineTooltipItem> _buildMultiLineTooltipItems(List<LineBarSpot> touchedBarSpots) {
    final List<LineTooltipItem> tooltipItems = [];

    if (touchedBarSpots.isNotEmpty) {
      final index = touchedBarSpots.first.x.toInt();
      final day = index + 1;
      final month = DateTime.now().month; // 使用当前月份
      final timeRange = '${month}月${day}日';

      // 为每个触摸点创建对应的tooltip项
      final visibleStatuses = EmotionStatus.values.where((status) =>
          _visibleStatuses[status] == true).toList();

      for (int i = 0; i < touchedBarSpots.length; i++) {
        final barSpot = touchedBarSpots[i];

        if (i < visibleStatuses.length) {
          final status = visibleStatuses[i];
          final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

          // 第一个tooltip项包含时间范围，其他项只显示状态信息
          final displayText = i == 0
              ? '$timeRange\n${status.displayName}: ${barSpot.y.toInt()}次'
              : '${status.displayName}: ${barSpot.y.toInt()}次';

          tooltipItems.add(
            LineTooltipItem(
              displayText,
              TextStyle(
                color: i == 0 ? Colors.white : color,
                fontWeight: i == 0 ? FontWeight.bold : FontWeight.w600,
                fontSize: i == 0 ? 12 : 11,
              ),
            ),
          );
        } else {
          // 如果没有对应的状态，添加空的tooltip项以保持数量一致
          tooltipItems.add(
            LineTooltipItem(
              '',
              const TextStyle(color: Colors.transparent),
            ),
          );
        }
      }
    }

    return tooltipItems;
  }

  /// 构建情绪状态图例（多曲线图用）
  Widget _buildEmotionStatusLegend() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '情绪状态图例',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              SizedBox(height: 8),
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: EmotionStatus.values.map((status) {
                  final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));
                  final isVisible = _visibleStatuses[status] ?? true;

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _visibleStatuses[status] = !isVisible;
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isVisible ? color.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isVisible ? color : Colors.grey,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: isVisible ? color : Colors.grey,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 4),
                          Text(
                            status.displayName,
                            style: TextStyle(
                              fontSize: 11,
                              color: isVisible ? Colors.grey.shade700 : Colors.grey.shade500,
                              fontWeight: isVisible ? FontWeight.w500 : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final emotionLevels = _healthData['level']!.map((d) => d).toList();
    final avgLevel = emotionLevels.fold(0, (sum, level) => sum + level) / emotionLevels.length;

    final mostCommonEmotion = _getMostCommonEmotion();
    final emotionVariety = _getEmotionVariety();

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('平均情绪', avgLevel.toStringAsFixed(1), Colors.purple),
                  _buildStatItem('最常见', mostCommonEmotion.displayName,
                      Color(int.parse('0xFF${mostCommonEmotion.colorHex.substring(1)}'))),
                  _buildStatItem('情绪多样性', emotionVariety, Colors.orange),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('情绪稳定性', _getEmotionStability(), Colors.blue),
                  _buildStatItem('记录数', '${_healthData['level']!.length}', Colors.grey),
                  _buildStatItem('时间跨度', _getTimeSpan(), Colors.teal),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的情绪状态
  EmotionStatus _getMostCommonEmotion() {
    final emotionCounts = <EmotionStatus, int>{};
    for (final data in _healthData['level']!) {
      emotionCounts[EmotionStatus.values[data]] = (emotionCounts[EmotionStatus.values[data]] ?? 0) + 1;
    }
    return emotionCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// 获取情绪稳定性描述
  String _getEmotionStability() {
    final levels = _healthData['level']!.map((d) => d).toList() as List<int>;
    if (levels.isEmpty) return '无数据';
    
    final variance = _calculateVariance(levels);
    if (variance < 1.0) return '很稳定';
    if (variance < 2.0) return '较稳定';
    if (variance < 3.0) return '一般';
    return '波动大';
  }

  /// 计算方差
  double _calculateVariance(List<int> values) {
    final mean = values.fold<int>(0, (sum, value) => sum + value) / values.length;
    final squaredDiffs = values.map((value) => (value - mean) * (value - mean));
    return squaredDiffs.fold<double>(0, (sum, diff) => sum + diff) / values.length;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 获取情绪多样性描述
  String _getEmotionVariety() {
    final uniqueEmotions = _healthData['level']!.map((d) => d).toSet();
    final varietyCount = uniqueEmotions.length;

    if (varietyCount <= 2) return '单一';
    if (varietyCount <= 4) return '较少';
    if (varietyCount <= 6) return '丰富';
    return '很丰富';
  }

  /// 获取时间跨度描述
  String _getTimeSpan() {
    if (_healthData['level']!.isEmpty) return '无数据';

    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '24小时';
      case TimePeriod.week:
        return '7天';
      case TimePeriod.month:
        final daysInMonth = DateTime(_currentDate.year, _currentDate.month + 1, 0).day;
        return '${daysInMonth}天';
    }
  }
}
