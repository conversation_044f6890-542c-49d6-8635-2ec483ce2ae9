# Generated code do not commit.
file(TO_CMAKE_PATH "F:\\Flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "H:\\CodeBase\\PET_APP\\pet_care" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=F:\\Flutter\\flutter"
  "PROJECT_DIR=H:\\CodeBase\\PET_APP\\pet_care"
  "FLUTTER_ROOT=F:\\Flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=H:\\CodeBase\\PET_APP\\pet_care\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=H:\\CodeBase\\PET_APP\\pet_care"
  "FLUTTER_TARGET=H:\\CodeBase\\PET_APP\\pet_care\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=H:\\CodeBase\\PET_APP\\pet_care\\.dart_tool\\package_config.json"
)
