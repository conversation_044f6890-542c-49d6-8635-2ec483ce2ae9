import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../constants/health_detection.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 健康监测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() =>
      _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState
    extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.scatter;
  DateTime _currentDate = DateTime.now();
   Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  // 用于散点图游标线功能
  double? _touchedX;
  bool _showTooltip = false;
  String _tooltipText = '';
  Offset _tooltipPosition = Offset.zero;
  Timer? _tooltipTimer;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _tooltipTimer?.cancel();
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false).device?.deviceName ?? '';
    ChartService chartService = Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchHealthDetectionChartData(device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchHealthDetectionChartData(device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchHealthDetectionChartData(device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康监测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动切换图表类型
                final availableTypes =
                    ChartTypePresets.getHealthDetectionStatistics(period);
                if (availableTypes.isNotEmpty &&
                    !availableTypes.contains(_selectedChartType)) {
                  _selectedChartType = availableTypes.first;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表类型选择器
          ChartViewSelector(
            availableTypes:
                ChartTypePresets.getHealthDetectionStatistics(_selectedPeriod),
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),

          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedChartType == ChartViewType.scatter
                                  ? '健康异常监测分布'
                                  : '健康监测统计',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),

          // 统计信息
          //if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.scatter:
        return _buildScatterChart();
      case ChartViewType.horizontalBar:
        return _buildHorizontalBarChart();
      default:
        return _buildScatterChart();
    }
  }

  /// 处理图表触摸事件
  void _handleChartTouch(Offset localPosition) {
    // 取消之前的延迟隐藏定时器
    _tooltipTimer?.cancel();

    // 使用与CursorLinePainter相同的边距计算
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    const double leftMargin = 16.0; // 与CursorLinePainter保持一致
    const double rightMargin = 16.0;
    final double screenWidth = renderBox.size.width;
    final double chartWidth = screenWidth - leftMargin - rightMargin;
    final double chartLeft = leftMargin;

    // 计算触摸位置在图表中的相对位置
    final touchX = localPosition.dx - chartLeft;
    if (touchX < 0 || touchX > chartWidth) return; // 确保在图表范围内

    final relativeX = touchX / chartWidth;

    // 计算对应的时间索引
    double touchedX;
    if (_selectedPeriod == TimePeriod.day) {
      touchedX = (relativeX * 47).clamp(0, 47); // 日模式：0-47
    } else {
      touchedX = (relativeX * (_healthData.length - 1))
          .clamp(0, _healthData.length - 1.0);
    }

    // 生成tooltip内容
    final tooltipText = _generateTooltipText(touchedX.round());

    // 计算tooltip位置，智能调整避免超出边界
    const double tooltipWidth = 160; // 估算tooltip宽度
    const double tooltipHeight = 60; // 估算tooltip高度

    // 计算实际的游标线X位置（与CursorLinePainter一致）
    final double cursorLineX = chartLeft + (chartWidth * relativeX);

    double tooltipX;
    double tooltipY;

    // 水平位置计算：如果右侧空间不足，显示在左侧
    if (cursorLineX + tooltipWidth / 2 > screenWidth - 20) {
      // 显示在游标左侧
      tooltipX = cursorLineX - tooltipWidth - 10;
    } else if (cursorLineX - tooltipWidth / 2 < 20) {
      // 显示在游标右侧
      tooltipX = cursorLineX + 10;
    } else {
      // 居中显示
      tooltipX = cursorLineX - tooltipWidth / 2;
    }

    // 垂直位置计算：优先显示在上方，空间不足时显示在下方
    if (localPosition.dy - tooltipHeight - 10 < 10) {
      // 显示在游标下方
      tooltipY = localPosition.dy + 20;
    } else {
      // 显示在游标上方
      tooltipY = localPosition.dy - tooltipHeight - 10;
    }

    final tooltipPosition = Offset(
      tooltipX.clamp(10, screenWidth - tooltipWidth - 10),
      tooltipY.clamp(10, 400),
    );

    setState(() {
      _touchedX = touchedX;
      _showTooltip = true;
      _tooltipText = tooltipText;
      _tooltipPosition = tooltipPosition;
    });
  }

  /// 清除触摸状态
  void _clearTouch() {
    // 延迟隐藏tooltip，提供更好的用户体验
    _tooltipTimer = Timer(Duration(milliseconds: 1500), () {
      setState(() {
        _touchedX = null;
        _showTooltip = false;
      });
    });
  }

  /// 生成tooltip文本
  String _generateTooltipText(int index) {
    // 计算时间
    String timeRange;
    List<HealthDetection> matchingDetections = [];

    if (_selectedPeriod == TimePeriod.day) {
      // 日模式：30分钟间隔
      final hour = (index * 30) ~/ 60;
      final minute = (index * 30) % 60;
      timeRange =
          '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

      // 查找对应时间段的数据
      final targetMinutes = index * 30;
      final targetHour = targetMinutes ~/ 60;
      final targetMinute = targetMinutes % 60;

      for (final data in _healthData.values) {
        final dataHour = data.datetime.hour;
        final dataMinute = data.datetime.minute;

        // 检查是否在30分钟时间窗口内
        if (dataHour == targetHour && (dataMinute - targetMinute).abs() <= 15) {
          matchingDetections.addAll(data.healthDetections);
        }
      }
    } else {
      // 周模式和月模式
      if (index >= 0 && index < _healthData.keys.length) {
        final data = _healthData[HealthDetection.values[index].name]!;
        timeRange = _getBottomTitle(index);
        matchingDetections = data.healthDetections;
      } else {
        timeRange = '无数据';
      }
    }

    if (matchingDetections.isEmpty) {
      return '$timeRange\n无异常监测';
    }

    // 将检测项目按每行最多2个进行分组
    final detectionNames =
        matchingDetections.map((d) => d.displayName).toList();
    final lines = <String>[];
    for (int i = 0; i < detectionNames.length; i += 2) {
      final endIndex =
          (i + 2 > detectionNames.length) ? detectionNames.length : i + 2;
      final lineItems = detectionNames.sublist(i, endIndex);
      lines.add(lineItems.join('、'));
    }

    return '$timeRange\n${lines.join('\n')}';
  }

  /// 构建独立的游标线
  Widget _buildCursorLine() {
    if (_touchedX == null) return Container();

    // 计算游标线的X位置
    final maxX =
        _selectedPeriod == TimePeriod.day ? 47.0 : _healthData.length - 1.0;
    final xRatio = _touchedX! / maxX;

    return Positioned.fill(
      child: CustomPaint(
        painter: CursorLinePainter(
          xRatio: xRatio,
          color: Colors.blue.shade400,
        ),
      ),
    );
  }

  /// 构建自定义tooltip
  Widget _buildCustomTooltip() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black54, // 更透明的背景色
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        _tooltipText,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center, // 居中对齐
      ),
    );
  }

  /// 构建散点图（健康监测项目分布）
  Widget _buildScatterChart() {
    final scatterSpots = <ScatterSpot>[];
    final detectionColors = <HealthDetection, Color>{};

    // 为每种监测项目分配颜色
    for (final detection in HealthDetection.values) {
      detectionColors[detection] =
          Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
    }

    // 为日模式生成48个30分钟间隔的时间点（00:00-23:30）
    // if (_selectedPeriod == TimePeriod.day) {
    //   for (int timeIndex = 0; timeIndex < 48; timeIndex++) {
    //     // 查找对应时间段的数据
    //     final targetMinutes = timeIndex * 30;
    //     final targetHour = targetMinutes ~/ 60;
    //     final targetMinute = targetMinutes % 60;

    //     // 在现有数据中查找匹配的时间点
    //     for (int i = 0; i < _healthData.length; i++) {
    //       final data = _healthData[i];
    //       final dataHour = data.datetime.hour;
    //       final dataMinute = data.datetime.minute;

    //       // 检查是否在30分钟时间窗口内
    //       if (dataHour == targetHour &&
    //           (dataMinute - targetMinute).abs() <= 15) {
    //         for (final detection in data.healthDetections) {
    //           scatterSpots.add(
    //             ScatterSpot(
    //               timeIndex.toDouble(),
    //               HealthDetection.values.indexOf(detection).toDouble(),
    //               dotPainter: FlDotCirclePainter(
    //                 radius: 3,
    //                 color: detectionColors[detection]!,
    //               ),
    //             ),
    //           );
    //         }
    //         break; // 找到匹配的数据后跳出内层循环
    //       }
    //     }
    //   }
    // } else {
    //   // 周模式和月模式保持原有逻辑
    //   for (int i = 0; i < _healthData.length; i++) {
    //     final data = _healthData[i];
    //     for (final detection in data.healthDetections) {
    //       scatterSpots.add(
    //         ScatterSpot(
    //           i.toDouble(),
    //           HealthDetection.values.indexOf(detection).toDouble(),
    //           dotPainter: FlDotCirclePainter(
    //             radius: 3,
    //             color: detectionColors[detection]!,
    //           ),
    //         ),
    //       );
    //     }
    //   }
    // }

    for(int i=0;i<_healthData['fever'].length;i++){
      for(final status in HealthDetection.values){
        if(_healthData[status.name][i] == 0) continue;
        scatterSpots.add(
          ScatterSpot(
            i.toDouble(),
            HealthDetection.values.indexOf(status).toDouble(),
            dotPainter: FlDotCirclePainter(
              radius: 3,
              color: detectionColors[status]!,
            ),
          ),
        );
      }
    }
      
    if (scatterSpots.isEmpty) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: 2.0, // 设置宽高比为2:1
            child: Stack(
              children: [
                // 散点图
                ScatterChart(
                  ScatterChartData(
                    // 设置坐标轴范围
                    minX: 0,
                    maxX: _selectedPeriod == TimePeriod.day
                        ? 47
                        : _healthData.length - 1.0, // 动态设置maxX
                    minY: -0.5,
                    maxY: HealthDetection.values.length - 0.5,
                    gridData: FlGridData(
                      show: true,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: Colors.grey.shade300,
                          strokeWidth: 1,
                          dashArray: [5, 5], // 设置虚线样式
                        );
                      },
                      getDrawingVerticalLine: (value) {
                        // 只绘制灰色虚线网格，游标线单独处理
                        return FlLine(
                          color: Colors.grey.shade300,
                          strokeWidth: 1,
                          dashArray: [5, 5],
                        );
                      },
                    ),
                    titlesData: FlTitlesData(
                      leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                          interval: 12.0, // 每12个点显示一次标签（6小时间隔）
                          getTitlesWidget: (value, meta) {
                            return Padding(
                              padding:
                                  const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                              child: Text(
                                _getBottomTitle(value.toInt()),
                                style: TextStyle(
                                    fontSize: 10, color: Colors.grey.shade600),
                              ),
                            );
                          },
                        ),
                      ),
                      rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false)), // 隐藏右侧标题
                      topTitles:
                          AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    ),
                    borderData: FlBorderData(
                      show: true,
                      border: Border(
                        bottom:
                            BorderSide(color: Colors.grey.shade300, width: 1),
                        right:
                            BorderSide(color: Colors.grey.shade300, width: 1),
                        top: BorderSide.none,
                        left: BorderSide.none,
                      ),
                    ), // 显示底部和右边的浅灰色边框
                    scatterTouchData: ScatterTouchData(
                      enabled: false, // 禁用内置触摸处理
                    ),
                    scatterSpots: scatterSpots,
                  ),
                ),
                // 独立的游标线
                if (_touchedX != null) _buildCursorLine(),
                // 透明触摸层
                GestureDetector(
                  onTapDown: (details) =>
                      _handleChartTouch(details.localPosition),
                  onPanUpdate: (details) =>
                      _handleChartTouch(details.localPosition),
                  onPanEnd: (details) => _clearTouch(),
                  child: Container(
                    color: Colors.transparent,
                    child: SizedBox.expand(),
                  ),
                ),
                // 自定义tooltip
                if (_showTooltip)
                  Positioned(
                    left: _tooltipPosition.dx,
                    top: _tooltipPosition.dy,
                    child: _buildCustomTooltip(),
                  ),
              ],
            ),
          ),
        ),
        SizedBox(height: 16),
        // 底部图例
        _buildBottomLegend(detectionColors),
      ],
    );
  }

  /// 构建水平条形图（各类健康监测的出现次数）
  Widget _buildHorizontalBarChart() {
    // 统计各类健康监测的出现次数
    final detectionCounts = <HealthDetection, int>{};

    for(final status in HealthDetection.values){
      detectionCounts[status] = _healthData[status.name]!.fold(0, (prev, element) => prev + element);
    }

    // 确保所有10种监测项目都有数据（没有的设为0）
    for (final detection in HealthDetection.values) {
      detectionCounts.putIfAbsent(detection, () => 0);
    }

    if (detectionCounts.values.every((count) => count == 0)) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    // 按照固定顺序排列（从上到下）：发热、呼吸异常、运动不稳、步态不对称、步态规律性下降、夜醒、睡眠不足、运动复杂度异常、活动模式改变、kcal下降
    final sortedEntries = HealthDetection.values.map((detection) {
      return MapEntry(detection, detectionCounts[detection]!);
    }).toList();

    // 反转顺序，使发热在顶部
    // final reversedEntries = sortedEntries.reversed.toList();
    final reversedEntries = sortedEntries;

    // 计算最大值用于比例计算
    final maxCount =
        reversedEntries.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8), // 减少padding
        child: Column(
          children: reversedEntries.map((entry) {
            final detection = entry.key;
            final count = entry.value;
            final color =
                Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
            final percentage = maxCount > 0 ? count / maxCount : 0.0;

            return Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 0.5), // 进一步减少间距
                child: Row(
                  children: [
                    // 条形图区域
                    Expanded(
                      child: Stack(
                        children: [
                          // 背景条
                          Container(
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          // 数据条（彩色条纹，不包含标签）
                          FractionallySizedBox(
                            widthFactor: percentage,
                            child: Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          // 标签（在浅灰色背景的最左侧，最顶层显示）
                          Positioned(
                            left: 8,
                            top: 0,
                            bottom: 0,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                detection.displayName, // 在浅灰色背景最左侧的标签
                                style: TextStyle(
                                  color: Colors.white, // 白色文字，在最顶层显示
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 8),
                    // 外部数值显示（右侧）
                    SizedBox(
                      width: 30,
                      child: Text(
                        '$count',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建底部图例
  Widget _buildBottomLegend(Map<HealthDetection, Color> detectionColors) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: HealthDetection.values.map((detection) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8, // 缩小到30%（原来是12）
              height: 8,
              decoration: BoxDecoration(
                color: detectionColors[detection]!,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 4),
            Text(
              detection.displayName,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    List<String> weekdaysShort = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = index ~/ 4;
        final minute = (index % 4) * 15;
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return weekdaysShort[index];
      case TimePeriod.month:
        return '${index+1}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    // 统计健康监测信息
    final allDetections =
        _healthData.values.expand((data) => data.healthDetections).toList();
    final dangerousCount = allDetections.where((d) => d.isDangerous).length;
    final totalCount = allDetections.length;
    final feverCount =
        allDetections.where((d) => d == HealthDetection.fever).length;

    final mostCommonDetection = _getMostCommonDetection(allDetections as List<HealthDetection>);

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('总检测数', totalCount.toString(), Colors.blue),
                  _buildStatItem('危险异常', dangerousCount.toString(), Colors.red),
                  _buildStatItem('发热次数', feverCount.toString(), Colors.orange),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见', mostCommonDetection?.displayName ?? '无',
                      Colors.purple),
                  _buildStatItem(
                      '健康状态',
                      _getHealthStatus(dangerousCount, totalCount),
                      _getHealthStatusColor(dangerousCount, totalCount)),
                  _buildStatItem(
                      '监测天数', '${_selectedPeriod.displayName}', Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的健康监测
  HealthDetection? _getMostCommonDetection(List<HealthDetection> detections) {
    if (detections.isEmpty) return null;

    final detectionCounts = <HealthDetection, int>{};
    for (final detection in detections) {
      detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
    }

    return detectionCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// 获取健康状态描述
  String _getHealthStatus(int dangerousCount, int totalCount) {
    if (totalCount == 0) return '良好';

    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return '良好';
    if (dangerousRatio < 0.1) return '注意';
    if (dangerousRatio < 0.3) return '警告';
    return '危险';
  }

  /// 获取健康状态颜色
  Color _getHealthStatusColor(int dangerousCount, int totalCount) {
    if (totalCount == 0) return Colors.green;

    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return Colors.green;
    if (dangerousRatio < 0.1) return Colors.yellow.shade700;
    if (dangerousRatio < 0.3) return Colors.orange;
    return Colors.red;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}

/// 游标线绘制器
class CursorLinePainter extends CustomPainter {
  final double xRatio;
  final Color color;

  CursorLinePainter({
    required this.xRatio,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 根据fl_chart的实际布局计算边距
    // fl_chart默认会为标题和标签预留空间
    const double leftMargin = 16.0; // 左边距（像素）
    const double rightMargin = 16.0; // 右边距（像素）
    const double topMargin = 8.0; // 上边距（像素）
    const double bottomMargin = 48.0; // 下边距（给横坐标标签预留40+8像素）

    final double chartWidth = size.width - leftMargin - rightMargin;
    final double chartHeight = size.height - topMargin - bottomMargin;
    final double chartLeft = leftMargin;
    final double chartTop = topMargin;

    // 计算游标线的X位置
    final double cursorX = chartLeft + (chartWidth * xRatio);

    // 绘制游标线
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(cursorX, chartTop),
      Offset(cursorX, chartTop + chartHeight),
      paint,
    );
  }

  @override
  bool shouldRepaint(CursorLinePainter oldDelegate) {
    return oldDelegate.xRatio != xRatio || oldDelegate.color != color;
  }
}
